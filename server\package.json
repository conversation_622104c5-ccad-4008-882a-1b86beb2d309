{"name": "langgraph-mongodb-example", "version": "1.0.0", "main": "index.js", "scripts": {"dev": "npx ts-node index.ts", "seed": "npx ts-node seed-database.ts"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@langchain/core": "^0.3.59", "@langchain/google-genai": "^0.2.12", "@langchain/langgraph": "^0.3.4", "@langchain/langgraph-checkpoint-mongodb": "^0.0.6", "@langchain/mongodb": "^0.1.0", "@langchain/openai": "^0.5.13", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "langchain": "^0.2.17", "mongodb": "^6.8.0", "zod": "^3.23.8"}, "devDependencies": {"@types/cors": "^2.8.18", "@types/express": "^4.17.21", "@types/node": "^22.5.1", "ts-node": "^10.9.2", "typescript": "^5.5.4"}}