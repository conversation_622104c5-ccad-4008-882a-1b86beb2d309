/* Reset and base styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #f5f5f5;
}

.App {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Chat Widget Styles */
.chat-widget-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: auto;
  height: auto;
  background-color: #4a00e0;
  border-radius: 50%;
  box-shadow: 0 5px 40px rgba(0, 0, 0, 0.16);
  overflow: hidden;
  transition: all 0.3s ease;
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.chat-widget-container.open {
  bottom: 0;
  width: 350px;
  height: 500px;
  background-color: #fff;
  border-radius: 10px 10px 0 0;
}

.chat-button {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: #4a00e0;
  color: white;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 24px;
}

.chat-button:hover {
  background-color: #3700b3;
}

.chat-header {
  background: linear-gradient(135deg, #4a00e0 0%, #8e2de2 100%);
  color: white;
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chat-title {
  display: flex;
  align-items: center;
  gap: 10px;
}

.chat-title h3 {
  margin: 0;
  font-size: 16px;
}

.close-button {
  background: transparent;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 18px;
}

.chat-messages {
  flex: 1;
  padding: 15px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.message {
  max-width: 80%;
  padding: 10px 15px;
  border-radius: 18px;
  margin-bottom: 5px;
  font-size: 14px;
  line-height: 1.4;
}

.message-bot {
  align-self: flex-start;
  background-color: #f0f2f5;
  color: #333;
  border-bottom-left-radius: 5px;
}

.message-user {
  align-self: flex-end;
  background-color: #4a00e0;
  color: white;
  border-bottom-right-radius: 5px;
}

.chat-input-container {
  display: flex;
  padding: 10px;
  border-top: 1px solid #e6e6e6;
}

.message-input {
  flex: 1;
  padding: 10px 15px;
  border: 1px solid #e6e6e6;
  border-radius: 20px;
  outline: none;
  font-size: 14px;
}

.message-input:focus {
  border-color: #4a00e0;
}

.send-button {
  background-color: #4a00e0;
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-left: 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.send-button:hover {
  background-color: #3700b3;
}

.send-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}


/* E-commerce Store Styles */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.header {
  background-color: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.top-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
}

.logo {
  font-size: 24px;
  font-weight: bold;
  color: #4a00e0;
}

.search-bar {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 30px;
  padding: 8px 15px;
  width: 40%;
}

.search-bar input {
  border: none;
  background: transparent;
  width: 100%;
  padding: 5px 10px;
  outline: none;
  font-size: 14px;
}

.search-bar button {
  background: none;
  border: none;
  color: #777;
  cursor: pointer;
}

.nav-icons {
  display: flex;
  gap: 20px;
}

.nav-icons a {
  color: #333;
  position: relative;
}

.nav-icons .badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: #4a00e0;
  color: white;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
}

.nav-bar {
  display: flex;
  justify-content: center;
  border-top: 1px solid #eee;
}

.nav-bar ul {
  display: flex;
  list-style: none;
  padding: 0;
  margin: 0;
}

.nav-bar li {
  margin: 0 15px;
}

.nav-bar a {
  display: block;
  padding: 15px 10px;
  color: #333;
  text-decoration: none;
  font-weight: 500;
  position: relative;
}

.nav-bar a:hover, .nav-bar a.active {
  color: #4a00e0;
}

.nav-bar a:hover:after, .nav-bar a.active:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: #4a00e0;
}

.hero {
  background: linear-gradient(135deg, #4a00e0 0%, #8e2de2 100%);
  color: white;
  padding: 60px 0;
  text-align: center;
  margin-bottom: 40px;
}

.hero h1 {
  font-size: 36px;
  margin-bottom: 15px;
}

.hero p {
  font-size: 18px;
  margin-bottom: 30px;
  opacity: 0.9;
}

.hero button {
  background-color: white;
  color: #4a00e0;
  border: none;
  padding: 12px 25px;
  border-radius: 30px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s;
}

.hero button:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.footer {
  background-color: #f9f9f9;
  padding: 60px 0 30px;
  margin-top: 60px;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 30px;
  margin-bottom: 40px;
}

.footer-column h3 {
  font-size: 18px;
  margin-bottom: 20px;
  color: #333;
}

.footer-column ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-column li {
  margin-bottom: 10px;
}

.footer-column a {
  color: #666;
  text-decoration: none;
}

.footer-column a:hover {
  color: #4a00e0;
}

.copyright {
  text-align: center;
  padding-top: 30px;
  border-top: 1px solid #eee;
  color: #777;
  font-size: 14px;
}
